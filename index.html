<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Pradeep Yadav - Full-Stack Developer Portfolio. Creation without obsession is just noise.">
    <meta name="keywords" content="full-stack developer, web development, JavaScript, React, Node.js, portfolio, Pradeep Yadav">
    <meta name="author" content="Pradeep Yadav">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Pradeep Yadav - Full-Stack Developer">
    <meta property="og:description" content="Pradeep Yadav - Full-Stack Developer Portfolio. Creation without obsession is just noise.">
    <meta property="og:type" content="website">

    <title>Pradeep Yadav - Full-Stack Developer</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Stylesheet -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="nav" id="nav">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="#home" class="nav-logo">Pradeep Yadav</a>
                <span class="nav-subtitle">Full-Stack Developer</span>
            </div>
            <div class="nav-content">
                <ul class="nav-menu" id="nav-menu">
                    <li class="nav-item">
                        <a href="#home" class="nav-link">
                            <span class="nav-number">01</span>
                            <span class="nav-text">Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#about" class="nav-link">
                            <span class="nav-number">02</span>
                            <span class="nav-text">About</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#skills" class="nav-link">
                            <span class="nav-number">03</span>
                            <span class="nav-text">Skills</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#projects" class="nav-link">
                            <span class="nav-number">04</span>
                            <span class="nav-text">Projects</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">
                            <span class="nav-number">05</span>
                            <span class="nav-text">Contact</span>
                        </a>
                    </li>
                </ul>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="nav-toggle-text">Menu</span>
                    <div class="nav-toggle-lines">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="hero" id="home">
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-name">Pradeep Yadav</h1>
                        <div class="hero-divider"></div>
                        <p class="hero-title">Full-Stack Developer</p>
                        <p class="hero-bio">"Creation without obsession is just noise."</p>
                    </div>
                    <div class="hero-meta">
                        <span class="hero-location">Based in India</span>
                        <span class="hero-status">Available for Work</span>
                    </div>
                </div>
                <div class="hero-scroll">
                    <div class="scroll-line"></div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about" id="about">
            <div class="container">
                <div class="about-grid">
                    <div class="about-intro">
                        <h2 class="about-title">About</h2>
                        <div class="about-divider"></div>
                    </div>
                    <div class="about-content">
                        <div class="about-text">
                            <p class="about-paragraph">
                                I specialize in building digital experiences that combine clean design with robust functionality.
                                My work spans the full development stack, from crafting intuitive user interfaces to architecting
                                scalable backend systems.
                            </p>
                            <p class="about-paragraph">
                                With a focus on modern technologies and best practices, I create solutions that are not only
                                visually appealing but also performant, accessible, and maintainable.
                            </p>
                        </div>
                        <div class="about-details">
                            <div class="detail-item">
                                <span class="detail-label">Experience</span>
                                <span class="detail-value">3+ Years</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Focus</span>
                                <span class="detail-value">Full-Stack Development</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Approach</span>
                                <span class="detail-value">User-Centered Design</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Skills Section -->
        <section class="skills" id="skills">
            <div class="container">
                <div class="skills-grid">
                    <div class="skills-intro">
                        <h2 class="skills-title">Skills</h2>
                        <div class="skills-divider"></div>
                    </div>
                    <div class="skills-content">
                        <div class="skills-text">
                            <p class="skills-paragraph">
                                I work across the full development stack, combining modern frontend frameworks
                                with robust backend technologies to create comprehensive digital solutions.
                            </p>
                        </div>
                        <div class="skills-categories">
                            <div class="skill-category">
                                <h3 class="category-title">Frontend Development</h3>
                                <div class="skill-list">
                                    <div class="skill-item">
                                        <span class="skill-name">React</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">JavaScript</span>
                                        <span class="skill-level">Expert</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">TypeScript</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">CSS3</span>
                                        <span class="skill-level">Expert</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">Vue.js</span>
                                        <span class="skill-level">Intermediate</span>
                                    </div>
                                </div>
                            </div>
                            <div class="skill-category">
                                <h3 class="category-title">Backend Development</h3>
                                <div class="skill-list">
                                    <div class="skill-item">
                                        <span class="skill-name">Node.js</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">Python</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">PostgreSQL</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">MongoDB</span>
                                        <span class="skill-level">Intermediate</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">AWS</span>
                                        <span class="skill-level">Intermediate</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section class="projects" id="projects">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Featured Projects</h2>
                    <p class="section-subtitle">Some of my recent work</p>
                </div>
                <div class="projects-grid">
                    <article class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">01</div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">E-Commerce Platform</h3>
                            <p class="project-description">
                                A full-stack e-commerce solution built with React, Node.js, and MongoDB.
                                Features include user authentication, payment processing, and admin dashboard.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">React</span>
                                <span class="tech-tag">Node.js</span>
                                <span class="tech-tag">MongoDB</span>
                                <span class="tech-tag">Stripe</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>Live Demo</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                        <polyline points="15,3 21,3 21,9"></polyline>
                                        <line x1="10" y1="14" x2="21" y2="3"></line>
                                    </svg>
                                </a>
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>GitHub</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>

                    <article class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">02</div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Task Management App</h3>
                            <p class="project-description">
                                A collaborative task management application with real-time updates,
                                drag-and-drop functionality, and team collaboration features.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Vue.js</span>
                                <span class="tech-tag">Express</span>
                                <span class="tech-tag">Socket.io</span>
                                <span class="tech-tag">PostgreSQL</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>Live Demo</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                        <polyline points="15,3 21,3 21,9"></polyline>
                                        <line x1="10" y1="14" x2="21" y2="3"></line>
                                    </svg>
                                </a>
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>GitHub</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>

                    <article class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">03</div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Analytics Dashboard</h3>
                            <p class="project-description">
                                A comprehensive analytics dashboard with interactive charts,
                                real-time data visualization, and customizable reporting features.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">React</span>
                                <span class="tech-tag">D3.js</span>
                                <span class="tech-tag">Python</span>
                                <span class="tech-tag">FastAPI</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>Live Demo</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                        <polyline points="15,3 21,3 21,9"></polyline>
                                        <line x1="10" y1="14" x2="21" y2="3"></line>
                                    </svg>
                                </a>
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>GitHub</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>

                    <article class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">04</div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Music Streaming App</h3>
                            <p class="project-description">
                                A modern music streaming application with playlist management,
                                social features, and high-quality audio streaming capabilities.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">TypeScript</span>
                                <span class="tech-tag">Next.js</span>
                                <span class="tech-tag">Prisma</span>
                                <span class="tech-tag">AWS S3</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>Live Demo</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                        <polyline points="15,3 21,3 21,9"></polyline>
                                        <line x1="10" y1="14" x2="21" y2="3"></line>
                                    </svg>
                                </a>
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>GitHub</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact" id="contact">
            <div class="container">
                <div class="contact-grid">
                    <div class="contact-intro">
                        <h2 class="contact-title">Contact</h2>
                        <div class="contact-divider"></div>
                    </div>
                    <div class="contact-content">
                        <div class="contact-text">
                            <p class="contact-paragraph">
                                I'm always interested in discussing new opportunities, collaborating on interesting projects,
                                or simply connecting with fellow developers and designers.
                            </p>
                        </div>
                        <div class="contact-details">
                            <div class="contact-item">
                                <span class="contact-label">Email</span>
                                <a href="mailto:<EMAIL>" class="contact-value"><EMAIL></a>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">LinkedIn</span>
                                <a href="https://linkedin.com/in/pradeepx3021" class="contact-value" target="_blank" rel="noopener noreferrer">linkedin.com/in/pradeepx3021</a>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">GitHub</span>
                                <a href="https://github.com/pradeepx3021" class="contact-value" target="_blank" rel="noopener noreferrer">github.com/pradeepx3021</a>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">Resume</span>
                                <a href="resume.pdf" class="contact-value" target="_blank" rel="noopener noreferrer">Download PDF</a>
                            </div>
                        </div>
                        <div class="contact-availability">
                            <div class="availability-item">
                                <span class="availability-label">Current Status</span>
                                <span class="availability-value">Available for Work</span>
                            </div>
                            <div class="availability-item">
                                <span class="availability-label">Response Time</span>
                                <span class="availability-value">Within 24 Hours</span>
                            </div>
                            <div class="availability-item">
                                <span class="availability-label">Preferred Contact</span>
                                <span class="availability-value">Email</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-brand">
                    <h3 class="footer-title">Pradeep Yadav</h3>
                    <p class="footer-subtitle">Full-Stack Developer</p>
                </div>
                <div class="footer-content">
                    <div class="footer-section">
                        <h4 class="footer-section-title">Navigation</h4>
                        <ul class="footer-links">
                            <li><a href="#home" class="footer-link">Home</a></li>
                            <li><a href="#about" class="footer-link">About</a></li>
                            <li><a href="#skills" class="footer-link">Skills</a></li>
                            <li><a href="#projects" class="footer-link">Projects</a></li>
                            <li><a href="#contact" class="footer-link">Contact</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-section-title">Connect</h4>
                        <ul class="footer-links">
                            <li><a href="mailto:<EMAIL>" class="footer-link">Email</a></li>
                            <li><a href="https://linkedin.com/in/pradeepx3021" class="footer-link" target="_blank" rel="noopener noreferrer">LinkedIn</a></li>
                            <li><a href="https://github.com/pradeepx3021" class="footer-link" target="_blank" rel="noopener noreferrer">GitHub</a></li>
                            <li><a href="resume.pdf" class="footer-link" target="_blank" rel="noopener noreferrer">Resume</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-section-title">Info</h4>
                        <ul class="footer-links">
                            <li><span class="footer-text">Based in India</span></li>
                            <li><span class="footer-text">Available for Work</span></li>
                            <li><span class="footer-text">© 2024 Pradeep Yadav</span></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-divider"></div>
                <div class="footer-bottom-content">
                    <p class="footer-copyright">Designed & developed with attention to detail</p>
                    <a href="#home" class="footer-top">
                        <span>Back to Top</span>
                        <div class="footer-arrow"></div>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>