<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Full-Stack Developer Portfolio - Modern web applications with clean, efficient code">
    <meta name="keywords" content="full-stack developer, web development, JavaScript, React, Node.js, portfolio">
    <meta name="author" content="Your Name">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Your Name - Full-Stack Developer">
    <meta property="og:description" content="Full-Stack Developer Portfolio - Modern web applications with clean, efficient code">
    <meta property="og:type" content="website">

    <title>Your Name - Full-Stack Developer</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Stylesheet -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="nav" id="nav">
        <div class="nav-container">
            <a href="#home" class="nav-logo">YN</a>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">Skills</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="hero" id="home">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="hero-greeting">Hello, I'm</span>
                        <span class="hero-name">Your Name</span>
                    </h1>
                    <p class="hero-subtitle">Full-Stack Developer</p>
                    <p class="hero-description">
                        I craft modern web applications with clean, efficient code and intuitive user experiences.
                    </p>
                    <div class="hero-buttons">
                        <a href="#projects" class="btn btn-primary">View My Work</a>
                        <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                    </div>
                </div>
                <div class="hero-scroll">
                    <a href="#about" class="scroll-indicator">
                        <span class="scroll-text">Scroll Down</span>
                        <div class="scroll-arrow"></div>
                    </a>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about" id="about">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">About Me</h2>
                    <p class="section-subtitle">Passionate about creating digital solutions</p>
                </div>
                <div class="about-content">
                    <div class="about-text">
                        <p class="about-paragraph">
                            I'm a passionate full-stack developer with a keen eye for design and a love for clean,
                            efficient code. With expertise spanning both frontend and backend technologies, I enjoy
                            building complete web applications from concept to deployment.
                        </p>
                        <p class="about-paragraph">
                            My approach combines technical proficiency with user-centered design principles,
                            ensuring that every project not only functions flawlessly but also provides an
                            exceptional user experience.
                        </p>
                        <div class="about-stats">
                            <div class="stat">
                                <span class="stat-number">3+</span>
                                <span class="stat-label">Years Experience</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">50+</span>
                                <span class="stat-label">Projects Completed</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">100%</span>
                                <span class="stat-label">Client Satisfaction</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Skills Section -->
        <section class="skills" id="skills">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Skills & Technologies</h2>
                    <p class="section-subtitle">Tools and technologies I work with</p>
                </div>
                <div class="skills-content">
                    <div class="skills-category">
                        <h3 class="skills-category-title">Frontend</h3>
                        <div class="skills-grid">
                            <div class="skill-item">
                                <div class="skill-icon">⚛️</div>
                                <span class="skill-name">React</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">📱</div>
                                <span class="skill-name">JavaScript</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">🎨</div>
                                <span class="skill-name">CSS3</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">📄</div>
                                <span class="skill-name">HTML5</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">🔷</div>
                                <span class="skill-name">TypeScript</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">⚡</div>
                                <span class="skill-name">Vue.js</span>
                            </div>
                        </div>
                    </div>
                    <div class="skills-category">
                        <h3 class="skills-category-title">Backend</h3>
                        <div class="skills-grid">
                            <div class="skill-item">
                                <div class="skill-icon">🟢</div>
                                <span class="skill-name">Node.js</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">🐍</div>
                                <span class="skill-name">Python</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">🗄️</div>
                                <span class="skill-name">MongoDB</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">🐘</div>
                                <span class="skill-name">PostgreSQL</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">🚀</div>
                                <span class="skill-name">Express.js</span>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">☁️</div>
                                <span class="skill-name">AWS</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section class="projects" id="projects">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Featured Projects</h2>
                    <p class="section-subtitle">Some of my recent work</p>
                </div>
                <div class="projects-grid">
                    <article class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">🌐</div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">E-Commerce Platform</h3>
                            <p class="project-description">
                                A full-stack e-commerce solution built with React, Node.js, and MongoDB.
                                Features include user authentication, payment processing, and admin dashboard.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">React</span>
                                <span class="tech-tag">Node.js</span>
                                <span class="tech-tag">MongoDB</span>
                                <span class="tech-tag">Stripe</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>Live Demo</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                        <polyline points="15,3 21,3 21,9"></polyline>
                                        <line x1="10" y1="14" x2="21" y2="3"></line>
                                    </svg>
                                </a>
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>GitHub</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>

                    <article class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">📱</div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Task Management App</h3>
                            <p class="project-description">
                                A collaborative task management application with real-time updates,
                                drag-and-drop functionality, and team collaboration features.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Vue.js</span>
                                <span class="tech-tag">Express</span>
                                <span class="tech-tag">Socket.io</span>
                                <span class="tech-tag">PostgreSQL</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>Live Demo</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                        <polyline points="15,3 21,3 21,9"></polyline>
                                        <line x1="10" y1="14" x2="21" y2="3"></line>
                                    </svg>
                                </a>
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>GitHub</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>

                    <article class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">📊</div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Analytics Dashboard</h3>
                            <p class="project-description">
                                A comprehensive analytics dashboard with interactive charts,
                                real-time data visualization, and customizable reporting features.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">React</span>
                                <span class="tech-tag">D3.js</span>
                                <span class="tech-tag">Python</span>
                                <span class="tech-tag">FastAPI</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>Live Demo</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                        <polyline points="15,3 21,3 21,9"></polyline>
                                        <line x1="10" y1="14" x2="21" y2="3"></line>
                                    </svg>
                                </a>
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>GitHub</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>

                    <article class="project-card">
                        <div class="project-image">
                            <div class="project-placeholder">🎵</div>
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Music Streaming App</h3>
                            <p class="project-description">
                                A modern music streaming application with playlist management,
                                social features, and high-quality audio streaming capabilities.
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">TypeScript</span>
                                <span class="tech-tag">Next.js</span>
                                <span class="tech-tag">Prisma</span>
                                <span class="tech-tag">AWS S3</span>
                            </div>
                            <div class="project-links">
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>Live Demo</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                        <polyline points="15,3 21,3 21,9"></polyline>
                                        <line x1="10" y1="14" x2="21" y2="3"></line>
                                    </svg>
                                </a>
                                <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">
                                    <span>GitHub</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact" id="contact">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Let's Work Together</h2>
                    <p class="section-subtitle">Ready to bring your ideas to life</p>
                </div>
                <div class="contact-content">
                    <div class="contact-info">
                        <p class="contact-description">
                            I'm always interested in new opportunities and exciting projects.
                            Whether you have a question or just want to say hi, feel free to reach out!
                        </p>
                        <div class="contact-methods">
                            <a href="mailto:<EMAIL>" class="contact-method">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                        <polyline points="22,6 12,13 2,6"></polyline>
                                    </svg>
                                </div>
                                <div class="contact-details">
                                    <span class="contact-label">Email</span>
                                    <span class="contact-value"><EMAIL></span>
                                </div>
                            </a>
                            <a href="https://linkedin.com/in/yourprofile" class="contact-method" target="_blank" rel="noopener noreferrer">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                        <rect x="2" y="9" width="4" height="12"></rect>
                                        <circle cx="4" cy="4" r="2"></circle>
                                    </svg>
                                </div>
                                <div class="contact-details">
                                    <span class="contact-label">LinkedIn</span>
                                    <span class="contact-value">Connect with me</span>
                                </div>
                            </a>
                            <a href="https://github.com/yourusername" class="contact-method" target="_blank" rel="noopener noreferrer">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                                    </svg>
                                </div>
                                <div class="contact-details">
                                    <span class="contact-label">GitHub</span>
                                    <span class="contact-value">View my code</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="contact-cta">
                        <a href="mailto:<EMAIL>" class="btn btn-primary btn-large">
                            Send Message
                        </a>
                        <a href="resume.pdf" class="btn btn-secondary btn-large" target="_blank" rel="noopener noreferrer">
                            Download Resume
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p class="footer-text">
                    © 2024 Your Name. Designed & built with passion.
                </p>
                <div class="footer-links">
                    <a href="#home" class="footer-link">Back to Top</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>