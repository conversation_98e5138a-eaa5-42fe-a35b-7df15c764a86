<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Pradeep Yadav - Full-Stack Developer Portfolio. Creation without obsession is just noise.">
    <meta name="keywords" content="full-stack developer, web development, JavaScript, React, Node.js, portfolio, Pradeep Yadav">
    <meta name="author" content="Pradeep Yadav">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Pradeep Yadav - Full-Stack Developer">
    <meta property="og:description" content="Pradeep Yadav - Full-Stack Developer Portfolio. Creation without obsession is just noise.">
    <meta property="og:type" content="website">

    <title>Pradeep Yadav - Full-Stack Developer</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Stylesheet -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="nav" id="nav">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="#home" class="nav-logo">Pradeep Yadav</a>
                <span class="nav-subtitle">Full-Stack Developer</span>
            </div>
            <div class="nav-content">
                <ul class="nav-menu" id="nav-menu">
                    <li class="nav-item">
                        <a href="#home" class="nav-link">
                            <span class="nav-number">01</span>
                            <span class="nav-text">Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#about" class="nav-link">
                            <span class="nav-number">02</span>
                            <span class="nav-text">About</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#skills" class="nav-link">
                            <span class="nav-number">03</span>
                            <span class="nav-text">Skills</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#projects" class="nav-link">
                            <span class="nav-number">04</span>
                            <span class="nav-text">Projects</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">
                            <span class="nav-number">05</span>
                            <span class="nav-text">Contact</span>
                        </a>
                    </li>
                </ul>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="nav-toggle-text">Menu</span>
                    <div class="nav-toggle-lines">
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="hero" id="home">
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-name">Pradeep Yadav</h1>
                        <div class="hero-divider"></div>
                        <p class="hero-title">Full-Stack Developer</p>
                        <p class="hero-bio">"Creation without obsession is just noise."</p>
                    </div>
                    <div class="hero-meta">
                        <span class="hero-location">Based in India</span>
                        <span class="hero-status">Available for Work</span>
                    </div>
                </div>
                <div class="hero-scroll">
                    <div class="scroll-line"></div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about" id="about">
            <div class="container">
                <div class="about-grid">
                    <div class="about-intro">
                        <h2 class="about-title">About</h2>
                        <div class="about-divider"></div>
                    </div>
                    <div class="about-content">
                        <div class="about-text">
                            <p class="about-paragraph">
                                I specialize in building digital experiences that combine clean design with robust functionality.
                                My work spans the full development stack, from crafting intuitive user interfaces to architecting
                                scalable backend systems.
                            </p>
                            <p class="about-paragraph">
                                With a focus on modern technologies and best practices, I create solutions that are not only
                                visually appealing but also performant, accessible, and maintainable.
                            </p>
                        </div>
                        <div class="about-details">
                            <div class="detail-item">
                                <span class="detail-label">Experience</span>
                                <span class="detail-value">3+ Years</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Focus</span>
                                <span class="detail-value">Full-Stack Development</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Approach</span>
                                <span class="detail-value">User-Centered Design</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Skills Section -->
        <section class="skills" id="skills">
            <div class="container">
                <div class="skills-grid">
                    <div class="skills-intro">
                        <h2 class="skills-title">Skills</h2>
                        <div class="skills-divider"></div>
                    </div>
                    <div class="skills-content">
                        <div class="skills-text">
                            <p class="skills-paragraph">
                                I work across the full development stack, combining modern frontend frameworks
                                with robust backend technologies to create comprehensive digital solutions.
                            </p>
                        </div>
                        <div class="skills-categories">
                            <div class="skill-category">
                                <h3 class="category-title">Frontend Development</h3>
                                <div class="skill-list">
                                    <div class="skill-item">
                                        <span class="skill-name">React</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">JavaScript</span>
                                        <span class="skill-level">Expert</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">TypeScript</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">CSS3</span>
                                        <span class="skill-level">Expert</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">Vue.js</span>
                                        <span class="skill-level">Intermediate</span>
                                    </div>
                                </div>
                            </div>
                            <div class="skill-category">
                                <h3 class="category-title">Backend Development</h3>
                                <div class="skill-list">
                                    <div class="skill-item">
                                        <span class="skill-name">Node.js</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">Python</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">PostgreSQL</span>
                                        <span class="skill-level">Advanced</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">MongoDB</span>
                                        <span class="skill-level">Intermediate</span>
                                    </div>
                                    <div class="skill-item">
                                        <span class="skill-name">AWS</span>
                                        <span class="skill-level">Intermediate</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section class="projects" id="projects">
            <div class="container">
                <div class="projects-grid">
                    <div class="projects-intro">
                        <h2 class="projects-title">Projects</h2>
                        <div class="projects-divider"></div>
                    </div>
                    <div class="projects-content">
                        <div class="projects-text">
                            <p class="projects-paragraph">
                                A selection of projects that showcase my technical skills and approach to problem-solving.
                                Each project demonstrates different aspects of full-stack development.
                            </p>
                        </div>
                        <div class="projects-list">
                            <article class="project-item">
                                <div class="project-header">
                                    <div class="project-number">01</div>
                                    <div class="project-meta">
                                        <h3 class="project-title">E-Commerce Platform</h3>
                                        <span class="project-year">2024</span>
                                    </div>
                                </div>
                                <div class="project-details">
                                    <div class="project-image">
                                        <img src="assets/project-01.png" alt="E-Commerce Platform Screenshot" class="project-img">
                                        <!-- Fallback placeholder if image doesn't load -->
                                        <div class="project-placeholder">01</div>
                                    </div>
                                    <div class="project-info">
                                        <p class="project-description">
                                            A comprehensive e-commerce solution featuring user authentication, payment processing,
                                            inventory management, and an admin dashboard. Built with modern technologies for
                                            scalability and performance.
                                        </p>
                                        <div class="project-tech">
                                            <span class="tech-item">React</span>
                                            <span class="tech-item">Node.js</span>
                                            <span class="tech-item">MongoDB</span>
                                            <span class="tech-item">Stripe</span>
                                        </div>
                                        <div class="project-links">
                                            <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">Live Demo</a>
                                            <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">GitHub</a>
                                        </div>
                                    </div>
                                </div>
                            </article>

                            <article class="project-item">
                                <div class="project-header">
                                    <div class="project-number">02</div>
                                    <div class="project-meta">
                                        <h3 class="project-title">Task Management App</h3>
                                        <span class="project-year">2024</span>
                                    </div>
                                </div>
                                <div class="project-details">
                                    <div class="project-image">
                                        <img src="assets/project-02.jpg" alt="Task Management App Screenshot" class="project-img">
                                        <!-- Fallback placeholder if image doesn't load -->
                                        <div class="project-placeholder">02</div>
                                    </div>
                                    <div class="project-info">
                                        <p class="project-description">
                                            A collaborative task management application with real-time updates, drag-and-drop
                                            functionality, and team collaboration features. Designed for productivity and
                                            seamless workflow management.
                                        </p>
                                        <div class="project-tech">
                                            <span class="tech-item">Vue.js</span>
                                            <span class="tech-item">Express</span>
                                            <span class="tech-item">Socket.io</span>
                                            <span class="tech-item">PostgreSQL</span>
                                        </div>
                                        <div class="project-links">
                                            <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">Live Demo</a>
                                            <a href="#" class="project-link" target="_blank" rel="noopener noreferrer">GitHub</a>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact" id="contact">
            <div class="container">
                <div class="contact-grid">
                    <div class="contact-intro">
                        <h2 class="contact-title">Contact</h2>
                        <div class="contact-divider"></div>
                    </div>
                    <div class="contact-content">
                        <div class="contact-text">
                            <p class="contact-paragraph">
                                I'm always interested in discussing new opportunities, collaborating on interesting projects,
                                or simply connecting with fellow developers and designers.
                            </p>
                        </div>
                        <div class="contact-details">
                            <div class="contact-item">
                                <span class="contact-label">Email</span>
                                <a href="mailto:<EMAIL>" class="contact-value"><EMAIL></a>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">LinkedIn</span>
                                <a href="https://linkedin.com/in/pradeepx3021" class="contact-value" target="_blank" rel="noopener noreferrer">linkedin.com/in/pradeepx3021</a>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">GitHub</span>
                                <a href="https://github.com/pradeepx3021" class="contact-value" target="_blank" rel="noopener noreferrer">github.com/pradeepx3021</a>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">Resume</span>
                                <a href="resume.pdf" class="contact-value" target="_blank" rel="noopener noreferrer">Download PDF</a>
                            </div>
                        </div>
                        <div class="contact-availability">
                            <div class="availability-item">
                                <span class="availability-label">Current Status</span>
                                <span class="availability-value">Available for Work</span>
                            </div>
                            <div class="availability-item">
                                <span class="availability-label">Response Time</span>
                                <span class="availability-value">Within 24 Hours</span>
                            </div>
                            <div class="availability-item">
                                <span class="availability-label">Preferred Contact</span>
                                <span class="availability-value">Email</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-brand">
                    <h3 class="footer-title">Pradeep Yadav</h3>
                    <p class="footer-subtitle">Full-Stack Developer</p>
                </div>
                <div class="footer-content">
                    <div class="footer-section">
                        <h4 class="footer-section-title">Navigation</h4>
                        <ul class="footer-links">
                            <li><a href="#home" class="footer-link">Home</a></li>
                            <li><a href="#about" class="footer-link">About</a></li>
                            <li><a href="#skills" class="footer-link">Skills</a></li>
                            <li><a href="#projects" class="footer-link">Projects</a></li>
                            <li><a href="#contact" class="footer-link">Contact</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-section-title">Connect</h4>
                        <ul class="footer-links">
                            <li><a href="mailto:<EMAIL>" class="footer-link">Email</a></li>
                            <li><a href="https://linkedin.com/in/pradeepx3021" class="footer-link" target="_blank" rel="noopener noreferrer">LinkedIn</a></li>
                            <li><a href="https://github.com/pradeepx3021" class="footer-link" target="_blank" rel="noopener noreferrer">GitHub</a></li>
                            <li><a href="resume.pdf" class="footer-link" target="_blank" rel="noopener noreferrer">Resume</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-section-title">Info</h4>
                        <ul class="footer-links">
                            <li><span class="footer-text">Based in India</span></li>
                            <li><span class="footer-text">Available for Work</span></li>
                            <li><span class="footer-text">© 2024 Pradeep Yadav</span></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-divider"></div>
                <div class="footer-bottom-content">
                    <p class="footer-copyright">Designed & developed with attention to detail</p>
                    <a href="#home" class="footer-top">
                        <span>Back to Top</span>
                        <div class="footer-arrow"></div>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>