/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background-color: #ffffff;
    overflow-x: hidden;
}

/* ===== VARIABLES ===== */
:root {
    /* Colors - Minimalist Black & White */
    --primary-color: #000000;
    --primary-dark: #000000;
    --text-primary: #000000;
    --text-secondary: #666666;
    --text-muted: #999999;
    --background: #ffffff;
    --background-alt: #fafafa;
    --border-color: #e0e0e0;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.08);
    --shadow-large: 0 10px 25px rgba(0, 0, 0, 0.1);

    /* Spacing */
    --container-max-width: 1000px;
    --section-padding: 6rem 0;
    --section-padding-mobile: 4rem 0;

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all var(--transition-normal);
    cursor: pointer;
    border: 2px solid transparent;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: white;
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: none;
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
}

/* ===== NAVIGATION ===== */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    max-width: var(--container-max-width);
    margin: 0 auto;
}

.nav-logo {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    transition: opacity var(--transition-fast);
}

.nav-logo:hover {
    opacity: 0.7;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 0.25rem;
}

.bar {
    width: 1.5rem;
    height: 2px;
    background-color: var(--text-primary);
    transition: all var(--transition-normal);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    padding-top: 5rem;
}

.hero-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 60vh;
}

.hero-text {
    margin-bottom: 4rem;
}

.hero-name {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
    line-height: 0.9;
}

.hero-divider {
    width: 60px;
    height: 1px;
    background-color: var(--text-primary);
    margin-bottom: 2rem;
}

.hero-title {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: 400;
    margin: 0;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.hero-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
}

.hero-location,
.hero-status {
    letter-spacing: 0.025em;
}

.hero-scroll {
    position: absolute;
    bottom: 3rem;
    right: 0;
}

.scroll-line {
    width: 1px;
    height: 4rem;
    background-color: var(--text-muted);
    position: relative;
    animation: scroll-fade 3s infinite;
}

@keyframes scroll-fade {
    0%, 100% {
        opacity: 0.3;
    }
    50% {
        opacity: 1;
    }
}

/* ===== SECTIONS ===== */
section {
    padding: var(--section-padding);
}

.about {
    background-color: var(--background);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: start;
}

.about-intro {
    position: sticky;
    top: 8rem;
}

.about-title {
    font-size: var(--font-size-4xl);
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
}

.about-divider {
    width: 40px;
    height: 1px;
    background-color: var(--text-primary);
}

.about-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.about-paragraph {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 0;
}

.about-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

.detail-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
}

/* ===== SKILLS SECTION ===== */
.skills-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 4rem;
    max-width: 1000px;
    margin: 0 auto;
}

.skills-category-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
    text-align: center;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1.5rem;
}

.skill-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem 1.5rem;
    background-color: var(--background);
    border: 1px solid var(--border-color);
    border-radius: 0;
    transition: all var(--transition-normal);
    cursor: default;
}

.skill-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.skill-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    text-align: center;
    letter-spacing: 0.025em;
}

/* ===== PROJECTS SECTION ===== */
.projects {
    background-color: var(--background);
    border-bottom: 1px solid var(--border-color);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.project-card {
    background-color: var(--background);
    border: 1px solid var(--border-color);
    border-radius: 0;
    overflow: hidden;
    transition: all var(--transition-normal);
    cursor: default;
}

.project-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-3px);
}

.project-image {
    height: 200px;
    background: var(--background-alt);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid var(--border-color);
}

.project-placeholder {
    font-size: 3rem;
    font-weight: 300;
    color: var(--text-muted);
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.1em;
}

.project-content {
    padding: 2rem;
}

.project-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.tech-tag {
    background-color: transparent;
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 0;
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid var(--border-color);
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--font-size-sm);
    transition: opacity var(--transition-fast);
    border-bottom: 1px solid transparent;
}

.project-link:hover {
    opacity: 0.7;
    border-bottom-color: var(--text-primary);
}

.project-link svg {
    transition: transform var(--transition-fast);
}

.project-link:hover svg {
    transform: translateX(2px);
}

/* ===== CONTACT SECTION ===== */
.contact-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.contact-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.7;
}

.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 2rem 1.5rem;
    background-color: var(--background);
    border: 1px solid var(--border-color);
    border-radius: 0;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.contact-method:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.contact-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    background-color: var(--background-alt);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 0;
    flex-shrink: 0;
}

.contact-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.contact-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.contact-value {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.contact-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--text-primary);
    color: white;
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
}

.footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: white;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.5rem;
    }

    section {
        padding: var(--section-padding-mobile);
    }

    .container {
        padding: 0 1rem;
    }

    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--background);
        flex-direction: column;
        gap: 0;
        padding: 2rem 0;
        box-shadow: var(--shadow-medium);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-item {
        padding: 0.5rem 2rem;
    }

    .nav-link::after {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero {
        padding-top: 6rem;
        min-height: 90vh;
        align-items: flex-start;
        padding-top: 8rem;
    }

    .hero-content {
        min-height: 50vh;
    }

    .hero-text {
        margin-bottom: 2rem;
    }

    .hero-name {
        font-size: clamp(2.5rem, 12vw, 4rem);
        margin-bottom: 1.5rem;
    }

    .hero-divider {
        width: 40px;
        margin-bottom: 1.5rem;
    }

    .hero-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .hero-scroll {
        bottom: 2rem;
    }

    .scroll-line {
        height: 3rem;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .skills-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-methods {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-cta {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .hero-name {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .skill-item {
        padding: 1rem;
    }

    .project-content {
        padding: 1.5rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-intro {
        position: static;
    }

    .about-title {
        font-size: var(--font-size-3xl);
        margin-bottom: 1rem;
    }

    .about-content {
        gap: 2rem;
    }

    .about-details {
        padding-top: 1.5rem;
        gap: 1rem;
    }

    .detail-item {
        padding: 0.75rem 0;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Focus styles for keyboard navigation */
.nav-link:focus,
.btn:focus,
.project-link:focus,
.contact-method:focus,
.footer-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
        --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.3);
        --shadow-large: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
}