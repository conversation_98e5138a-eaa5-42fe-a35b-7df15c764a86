/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background-color: #ffffff;
    overflow-x: hidden;
}

/* ===== VARIABLES ===== */
:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --background: #ffffff;
    --background-alt: #f7fafc;
    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 10px 25px rgba(0, 0, 0, 0.1);

    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 5rem 0;
    --section-padding-mobile: 3rem 0;

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all var(--transition-normal);
    cursor: pointer;
    border: 2px solid transparent;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--background-alt);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
}

/* ===== NAVIGATION ===== */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    max-width: var(--container-max-width);
    margin: 0 auto;
}

.nav-logo {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.nav-logo:hover {
    color: var(--primary-dark);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 0.25rem;
}

.bar {
    width: 1.5rem;
    height: 2px;
    background-color: var(--text-primary);
    transition: all var(--transition-normal);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    padding-top: 5rem;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-greeting {
    display: block;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 400;
}

.hero-name {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.hero-subtitle {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-muted);
    transition: color var(--transition-normal);
}

.scroll-indicator:hover {
    color: var(--primary-color);
}

.scroll-text {
    font-size: var(--font-size-sm);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.scroll-arrow {
    width: 1px;
    height: 2rem;
    background-color: currentColor;
    position: relative;
    animation: scroll-bounce 2s infinite;
}

.scroll-arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 7px;
    height: 7px;
    border-right: 1px solid currentColor;
    border-bottom: 1px solid currentColor;
    transform: rotate(45deg);
}

@keyframes scroll-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== SECTIONS ===== */
section {
    padding: var(--section-padding);
}

.about {
    background-color: var(--background-alt);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-paragraph {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== SKILLS SECTION ===== */
.skills-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 4rem;
    max-width: 1000px;
    margin: 0 auto;
}

.skills-category-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
    text-align: center;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1.5rem;
}

.skill-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background-color: var(--background);
    border-radius: 1rem;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
    cursor: default;
}

.skill-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.skill-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.skill-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
}

/* ===== PROJECTS SECTION ===== */
.projects {
    background-color: var(--background-alt);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.project-card {
    background-color: var(--background);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
    cursor: default;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-large);
}

.project-image {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.project-placeholder {
    font-size: 4rem;
    opacity: 0.8;
}

.project-content {
    padding: 2rem;
}

.project-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.tech-tag {
    background-color: var(--background-alt);
    color: var(--text-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid var(--border-color);
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.project-link:hover {
    color: var(--primary-dark);
}

.project-link svg {
    transition: transform var(--transition-fast);
}

.project-link:hover svg {
    transform: translateX(2px);
}

/* ===== CONTACT SECTION ===== */
.contact-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.contact-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.7;
}

.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background-color: var(--background-alt);
    border-radius: 1rem;
    text-decoration: none;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.contact-method:hover {
    background-color: var(--background);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.contact-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: 0.75rem;
    flex-shrink: 0;
}

.contact-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.contact-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.contact-value {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.contact-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--text-primary);
    color: white;
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
}

.footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: white;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.5rem;
    }

    section {
        padding: var(--section-padding-mobile);
    }

    .container {
        padding: 0 1rem;
    }

    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--background);
        flex-direction: column;
        gap: 0;
        padding: 2rem 0;
        box-shadow: var(--shadow-medium);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-item {
        padding: 0.5rem 2rem;
    }

    .nav-link::after {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero {
        padding-top: 6rem;
        min-height: 90vh;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .skills-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-methods {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-cta {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .hero-name {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .skill-item {
        padding: 1rem;
    }

    .project-content {
        padding: 1.5rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Focus styles for keyboard navigation */
.nav-link:focus,
.btn:focus,
.project-link:focus,
.contact-method:focus,
.footer-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
        --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.3);
        --shadow-large: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
}