# Minimalist Portfolio Website

A clean, modern, and responsive portfolio website for full-stack developers built with vanilla HTML, CSS, and JavaScript.

## Features

### Design
- **Super minimalist aesthetic** with clean lines and generous white space
- **Modern, professional appearance** suitable for showcasing development skills
- **Monochromatic color palette** with a subtle blue accent color
- **Consistent typography** using the Inter font family

### Responsive Design
- **Mobile-first approach** ensuring optimal experience on all devices
- **CSS Grid and Flexbox** for flexible, modern layouts
- **Responsive navigation** with mobile hamburger menu
- **Optimized for desktop, tablet, and mobile** viewing

### Performance
- **Fast loading** with optimized CSS and minimal JavaScript
- **Efficient animations** using CSS transitions and transforms
- **Debounced scroll events** for smooth performance
- **Minimal dependencies** - only vanilla technologies

### Accessibility
- **Semantic HTML5** structure for screen readers
- **Keyboard navigation** support with proper focus management
- **ARIA labels** and proper heading hierarchy
- **Skip to main content** link for keyboard users
- **High contrast mode** support
- **Reduced motion** support for users with vestibular disorders

### Interactive Features
- **Smooth scrolling** navigation between sections
- **Active navigation highlighting** based on scroll position
- **Scroll animations** for cards and elements
- **Typing animation** for the hero section
- **Hover effects** with subtle transforms and shadows
- **Mobile-friendly touch interactions**

## Sections

1. **Header/Navigation** - Fixed navigation with smooth scroll links
2. **Hero Section** - Name, title, and call-to-action buttons
3. **About Section** - Professional introduction with statistics
4. **Skills Section** - Frontend and backend technologies showcase
5. **Projects Section** - Featured projects with descriptions and links
6. **Contact Section** - Contact methods and social links
7. **Footer** - Copyright and additional links

## Technologies Used

- **HTML5** - Semantic markup and structure
- **CSS3** - Modern styling with Grid, Flexbox, and custom properties
- **JavaScript (ES6+)** - Interactive features and animations
- **Google Fonts** - Inter font family for clean typography

## File Structure

```
portfoliox/
├── index.html          # Main HTML structure
├── styles.css          # All CSS styles and responsive design
├── script.js           # JavaScript functionality
├── assets/             # Images and favicon
├── resume.pdf          # Resume file (placeholder)
└── README.md           # Project documentation
```

## Customization

### Personal Information
1. Update the name and title in `index.html`
2. Replace placeholder email and social links
3. Add your actual resume file
4. Update the meta tags with your information

### Content
1. **About Section**: Customize the description and statistics
2. **Skills Section**: Update technologies and add/remove skills
3. **Projects Section**: Replace with your actual projects
4. **Contact Section**: Update contact methods and links

### Styling
1. **Colors**: Modify CSS custom properties in `:root`
2. **Fonts**: Change the Google Fonts import and font-family
3. **Spacing**: Adjust section padding and margins
4. **Animations**: Customize transition durations and effects

### Projects
To add new projects:
1. Copy the project card structure in HTML
2. Update the project details (title, description, tech stack, links)
3. Add project images to the assets folder
4. Update the project image src attributes

## Browser Support

- **Modern browsers** (Chrome, Firefox, Safari, Edge)
- **Mobile browsers** (iOS Safari, Chrome Mobile)
- **Graceful degradation** for older browsers

## Performance Optimization

- **Minimal HTTP requests** with inline SVG icons
- **Optimized images** (when added)
- **Efficient CSS** with minimal specificity
- **Debounced scroll events** for smooth performance
- **CSS custom properties** for consistent theming

## SEO Features

- **Semantic HTML** structure
- **Meta tags** for social sharing
- **Proper heading hierarchy**
- **Alt text** for images (when added)
- **Structured data** ready markup

## Getting Started

1. **Clone or download** the project files
2. **Customize** the content with your information
3. **Add your projects** and update the project section
4. **Replace placeholder links** with your actual profiles
5. **Add your resume** file to replace the placeholder
6. **Test** on different devices and browsers
7. **Deploy** to your preferred hosting platform

## Deployment

This website can be deployed to any static hosting service:
- **GitHub Pages**
- **Netlify**
- **Vercel**
- **AWS S3**
- **Traditional web hosting**

Simply upload all files to your hosting provider's web directory.

## License

This project is open source and available under the [MIT License](LICENSE).

## Credits

- **Design**: Custom minimalist design
- **Icons**: Inline SVG icons
- **Fonts**: Inter font family from Google Fonts
- **Inspiration**: Modern portfolio design trends

---

**Note**: Remember to replace all placeholder content with your actual information before deploying the website.
